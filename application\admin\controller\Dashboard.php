<?php

namespace app\admin\controller;

use app\admin\model\Admin;
use app\admin\model\aweme\Account;
use app\admin\model\Order;
use app\common\controller\Backend;
use fast\Date;

/**
 * 控制台
 *
 * @icon   fa fa-dashboard
 * @remark 用于展示当前系统中的统计数据、统计报表及重要实时数据
 */
class Dashboard extends Backend
{

    /**
     * 查看
     */
    public function index()
    {
        try {
            \think\Db::execute("SET @@sql_mode='';");
        } catch (\Exception $e) {

        }
        $where = [];

        if (in_array($this->auth->id, [1,2])) {
        } else {
            $where = ['admin_id' => $this->auth->id];
        }
        $today_data = (new \app\admin\model\Order)->where($where)
            ->whereTime('create_time', 'today')
            ->field('admin_id,ROUND(sum(IF(pay_status="1",amount,0)),2) as amount,sum(IF(pay_status="0",amount,0)) as amount_all,count(IF(pay_status="1",1,null)) as c1,count(IF(admin_id>0,1,null)) as c2')
            ->select();
        // 今日充值金额
        $topUpToday = $today_data[0]->amount ? "¥".$today_data[0]->amount:"¥0";
        // 今日充值成率
        $recharge_ratio = $today_data[0]->amount ? round($today_data[0]->c1 / $today_data[0]->c2,4) * 100 . '%' : '0%';
        // 正常账号数量
        $normal_count = db('account')->where($where)->where('status', 'normal')->count();
        // 异常账号数量
        $abnormal_count = db('account')->where($where)->where('statusinfo','like', '%异常%')->count();
        // 每日充值统计
        $recharge_list = (new \app\admin\model\Order)->where($where)->where('pay_status', '1')
            ->group('FROM_UNIXTIME(create_time, "%Y-%m-%d")')
            ->whereTime('create_time', '-7 days')
            ->field('FROM_UNIXTIME(create_time, "%Y-%m-%d") as date,ROUND(sum(amount),2) as amount')
            ->select();
        $this->view->assign([
            'recharge_list'     => $recharge_list,
            'normal_count'      => $normal_count,
            'abnormal_count'    => $abnormal_count,
            'today_amount'      => $topUpToday,
            'recharge_ratio'    => $recharge_ratio,
        ]);

        return $this->view->fetch();
    }

}
